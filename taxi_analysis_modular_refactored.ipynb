import warnings
from typing import Any

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

# import seaborn as sns

warnings.filterwarnings("ignore")

# Настройка стиля графиков
plt.style.use("default")
# sns.set_palette("husl")
plt.rcParams["figure.figsize"] = (12, 8)
plt.rcParams["font.size"] = 10

def load_data(file_path: str) -> pd.DataFrame:
    return pd.read_csv(file_path)


def validate_required_columns(df: pd.DataFrame, required_columns: list[str]) -> None:
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        msg = f"Отсутствуют обязательные колонки: {missing_columns}"
        raise ValueError(msg)


def check_duplicates(df: pd.DataFrame, id_column: str = "id_order") -> None:
    if id_column not in df.columns:
        return

    duplicates = df[id_column].duplicated().sum()

    if duplicates > 0:
        warnings.warn(f"Обнаружено {duplicates} дублированных ID", stacklevel=2)

def analyze_missing_values(df: pd.DataFrame, threshold: float = 50.0) -> None:
    """
    Анализирует пропущенные значения в DataFrame и выдает предупреждения
    для колонок с высоким процентом пропусков.
    """
    for column in df.columns:
        if column.endswith("_time"):
            continue

        missing_count = df[column].isna().sum()
        if missing_count > 0:
            missing_percentage = (missing_count / len(df)) * 100
            if missing_percentage > threshold:
                warnings.warn(f"Колонка {column} содержит более {threshold}% пропущенных значений", stacklevel=2)

def convert_time_column(series: pd.Series, time_format: str = "mixed") -> pd.Series:
    if series.dtype == "datetime64[ns]":
        return series

    return pd.to_datetime(series, format=time_format)


def convert_time_columns(df: pd.DataFrame, time_columns: list[str], time_format: str = "mixed") -> pd.DataFrame:
    df_result = df.copy()

    for col in time_columns:
        if col in df_result.columns:
            df_result[col] = convert_time_column(df_result[col], time_format)

    return df_result


def add_time_derived_columns(df: pd.DataFrame, base_column: str = "order_time") -> pd.DataFrame:
    df_result = df.copy()

    if base_column in df_result.columns:
        df_result["day_order"] = df_result[base_column].dt.date

    return df_result

def create_aggregation_config(df: pd.DataFrame) -> dict[str, tuple[str, str]]:
    agg_dict = {}

    column_mapping = {
        "cnt_order": "id_order",
        "cnt_offer": "offer_time",
        "cnt_assign": "assign_time",
        "cnt_arrive": "arrive_time",
        "cnt_trip": "trip_time",
    }

    for metric_name, column in column_mapping.items():
        if column in df.columns:
            agg_dict[metric_name] = (column, "count")

    return agg_dict


def group_and_aggregate(
    df: pd.DataFrame,
    group_by: str | list[str],
    agg_config: dict[str, tuple[str, str]],
) -> pd.DataFrame:
    agg_dict = {}
    for new_col_name, (source_col, agg_func) in agg_config.items():
        agg_dict[new_col_name] = (source_col, agg_func)

    return df.groupby(group_by, as_index=False).agg(**agg_dict)

def calculate_conversion(df: pd.DataFrame, numerator_col: str, denominator_col: str) -> pd.Series:
    if numerator_col not in df.columns:
        msg = f"Колонка {numerator_col} не найдена"
        raise ValueError(msg)
    if denominator_col not in df.columns:
        msg = f"Колонка {denominator_col} не найдена"
        raise ValueError(msg)

    return df[numerator_col] / df[denominator_col].replace(0, np.nan)


def calculate_all_conversions(df: pd.DataFrame) -> pd.DataFrame:
    df_result = df.copy()

    conversions = [
        ("cnt_trip", "cnt_order", "order2trip"),
        ("cnt_offer", "cnt_order", "order2offer"),
        ("cnt_assign", "cnt_offer", "offer2assign"),
        ("cnt_arrive", "cnt_assign", "assign2arrive"),
        ("cnt_trip", "cnt_arrive", "arrive2trip"),
    ]

    for num_col, den_col, conv_name in conversions:
        df_result[conv_name] = calculate_conversion(df_result, num_col, den_col)

    return df_result.replace([np.inf, -np.inf], np.nan)

def get_city_data(df: pd.DataFrame, cities: list[str] | None = None) -> tuple[list[str], list[pd.DataFrame]]:
    if "city" not in df.columns:
        msg = "Колонка 'city' не найдена в данных"
        raise ValueError(msg)

    available_cities = df["city"].unique().tolist()

    if cities is None:
        cities = available_cities
    else:
        missing_cities = [city for city in cities if city not in available_cities]
        if missing_cities:
            cities = [city for city in cities if city in available_cities]

    if not cities:
        msg = "Нет доступных городов для отображения"
        raise ValueError(msg)

    city_data_list = []
    for city in cities:
        city_data = df[df["city"] == city]
        city_data_list.append(city_data)

    return cities, city_data_list


def plot_city_lines(cities: list[str], city_data_list: list[pd.DataFrame], x_column: str, y_column: str) -> None:
    for city, city_data in zip(cities, city_data_list, strict=False):
        if len(city_data) == 0 or x_column not in city_data.columns or y_column not in city_data.columns:
            continue

        plt.plot(
            city_data[x_column],
            city_data[y_column],
            label=city,
            marker="o",
            linewidth=2,
            markersize=6,
        )


def setup_plot_labels(
    title: str | None,
    x_column: str,
    y_column: str,
    y_limit: tuple[float, float] | None = None,
) -> None:
    plt.legend(bbox_to_anchor=(1.05, 1), loc="upper left")
    plt.grid(visible=True, alpha=0.3)

    if title:
        plt.title(title, fontsize=14, fontweight="bold")
    else:
        plt.title(f"{y_column} по городам", fontsize=14, fontweight="bold")

    plt.xlabel(x_column.replace("_", " ").title(), fontsize=12)
    plt.ylabel(y_column.replace("_", " ").title(), fontsize=12)

    if y_limit:
        plt.ylim(y_limit)

    plt.tight_layout()


def plot_metric_by_cities(
    df: pd.DataFrame,
    metric_column: str,
    x_column: str = "day_order",
    cities: list[str] | None = None,
    title: str | None = None,
    y_limit: tuple[float, float] | None = None,
    figsize: tuple[int, int] = (12, 8),
    style: str = "seaborn-v0_8",
) -> None:
    required_cols = [metric_column, x_column]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        msg = f"Отсутствуют колонки: {missing_cols}"
        raise ValueError(msg)

    plt.style.use(style)
    plt.figure(figsize=figsize)
    cities, city_data_list = get_city_data(df, cities)
    plot_city_lines(cities, city_data_list, x_column, metric_column)
    setup_plot_labels(title, x_column, metric_column, y_limit)

    plt.show()

def detect_outliers_iqr(data: pd.Series, threshold: float = 1.5) -> pd.Series:
    q1 = data.quantile(0.25)
    q3 = data.quantile(0.75)
    iqr = q3 - q1

    lower_bound = q1 - threshold * iqr
    upper_bound = q3 + threshold * iqr

    return (data < lower_bound) | (data > upper_bound)


def analyze_column_outliers(df: pd.DataFrame, column: str, threshold: float = 1.5) -> dict[str, Any]:
    """
    Анализирует выбросы в одной колонке методом IQR.
    """
    if column not in df.columns:
        msg = f"Колонка {column} не найдена"
        raise ValueError(msg)

    data = df[column].dropna()
    if len(data) == 0:
        return {"column": column, "outliers_count": 0, "outliers_percentage": 0.0}

    outlier_mask = detect_outliers_iqr(data, threshold)
    outliers_count = outlier_mask.sum()
    outliers_percentage = (outliers_count / len(data)) * 100

    return {
        "column": column,
        "outliers_count": outliers_count,
        "outliers_percentage": outliers_percentage,
        "threshold": threshold,
        "outlier_indices": data[outlier_mask].index.tolist(),
    }

def validate_visualization_columns(df: pd.DataFrame) -> None:
    required_columns = ["cnt_order", "order2trip", "order2offer", "offer2assign", "assign2arrive", "arrive2trip"]

    missing_columns = [col for col in required_columns if col not in df.columns]

    if missing_columns:
        msg = f"Отсутствуют колонки для визуализации: {missing_columns}"
        raise ValueError(msg)

def perform_complete_analysis(
    file_path: str,
    required_columns: list[str] | None = None,
    time_columns: list[str] | None = None,
    group_by: str | list[str] | None = None,
) -> tuple[pd.DataFrame, pd.DataFrame]:
    """
    Выполняет полный анализ данных такси, используя модульные функции.
    """
    if group_by is None:
        group_by = ["day_order", "city"]
    df = load_data(file_path)

    if required_columns is None:
        required_columns = [
            "id_order",
            "order_time",
            "offer_time",
            "assign_time",
            "arrive_time",
            "trip_time",
            "city",
        ]

    validate_required_columns(df, required_columns)
    check_duplicates(df)
    analyze_missing_values(df)

    if time_columns is None:
        time_columns = [
            "order_time",
            "offer_time",
            "assign_time",
            "arrive_time",
            "trip_time",
        ]

    df = convert_time_columns(df, time_columns)
    df = add_time_derived_columns(df)

    agg_config = create_aggregation_config(df)
    df_metrics = group_and_aggregate(df, group_by, agg_config)
    df_metrics = calculate_all_conversions(df_metrics)

    return df, df_metrics

# Выполняем полный анализ
df_original, df_metrics = perform_complete_analysis("taxi_data.csv")

print(f"✓ Исходные данные: {df_original.shape}")
print(f"✓ Агрегированные метрики: {df_metrics.shape}")

# Валидация колонок для визуализации
validate_visualization_columns(df_metrics)
print("✓ Все необходимые колонки для визуализации присутствуют")

# График количества заказов
plot_metric_by_cities(df_metrics, "cnt_order", title="Количество заказов по городам")

# График конверсии заказ -> поездка
plot_metric_by_cities(df_metrics, "order2trip", title="Конверсия заказ -> поездка", y_limit=(0, 1))

# График конверсии заказ -> предложение
plot_metric_by_cities(df_metrics, "order2offer", title="Конверсия заказ -> предложение", y_limit=(0, 1))

# График конверсии предложение -> назначение
plot_metric_by_cities(df_metrics, "offer2assign", title="Конверсия предложение -> назначение", y_limit=(0, 1))

# График конверсии назначение -> прибытие
plot_metric_by_cities(df_metrics, "assign2arrive", title="Конверсия назначение -> прибытие", y_limit=(0, 1))

# График конверсии прибытие -> поездка
plot_metric_by_cities(df_metrics, "arrive2trip", title="Конверсия прибытие -> поездка", y_limit=(0, 1))

# Анализ выбросов в количестве заказов
outliers_result = analyze_column_outliers(df_metrics, "cnt_order")
print(
    f"Выбросы в колонке cnt_order: {outliers_result['outliers_count']} ({outliers_result['outliers_percentage']:.2f}%)"
)